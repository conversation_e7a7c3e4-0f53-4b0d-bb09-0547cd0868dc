import { rbacRequest } from '@/request';
import type { Unit } from '@/service/system';

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

type BaseApi = {
  getUnitTree: (params: Record<string, unknown>) => Promise<Unit[]>;
};

// 定义 API 接口
const baseApi: BaseApi = {
  getUnitTree: async (params) => {
    const resp: ApiResponse<Unit[]> = await rbacRequest.get('/unit/unitTree', {
      params,
    });
    return resp.data;
  },
};

export default baseApi;
