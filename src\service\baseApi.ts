import { rbacRequest } from '@/request';
import type { Unit } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 * @template T - 响应数据的类型
 * @description 定义所有API响应的统一格式，包含状态码、数据、消息和总数
 */
export type ApiResponse<T = unknown> = {
  /** 响应状态码，200表示成功 */
  code: number;
  /** 响应数据，类型由泛型T决定 */
  data: T;
  /** 响应消息，通常用于错误提示 */
  msg?: string;
  /** 数据总数，用于分页场景 */
  total?: number;
};

/**
 * 组织机构树查询参数接口
 * @description 定义获取组织机构树时可传递的查询参数
 */
export interface UnitTreeQueryParams {
  /** 组织机构ID，用于获取指定组织下的子机构 */
  unitId?: string;
  /** 组织机构类型，用于筛选特定类型的组织 */
  unitType?: string;
  /** 是否包含禁用的组织机构 */
  includeDisabled?: boolean;
  /** 最大层级深度 */
  maxDepth?: number;
  /** 其他扩展参数 */
  [key: string]: unknown;
}

/**
 * 基础API接口定义
 * @description 定义所有基础API方法的类型签名
 */
interface BaseApiInterface {
  /**
   * 获取组织机构树形数据
   * @param params - 查询参数
   * @returns Promise<Unit[]> - 返回格式化后的组织机构树数组
   */
  getUnitTree: (params?: UnitTreeQueryParams) => Promise<Unit[]>;
}

// ===== API 实现 =====

/**
 * 基础API服务
 * @description 提供基础的API调用方法，主要用于获取组织机构等基础数据
 */
const baseApi: BaseApiInterface = {
  /**
   * 获取组织机构树形数据
   * @param params - 查询参数，可选
   * @returns Promise<Unit[]> - 返回格式化后的组织机构树数组
   * @throws {Error} 当API调用失败时抛出错误
   *
   * @example
   * ```typescript
   * // 获取所有组织机构
   * const allUnits = await baseApi.getUnitTree();
   *
   * // 获取指定类型的组织机构
   * const companyUnits = await baseApi.getUnitTree({
   *   unitType: 'company'
   * });
   *
   * // 获取指定组织下的子机构
   * const subUnits = await baseApi.getUnitTree({
   *   unitId: 'parent-unit-id'
   * });
   * ```
   */
  getUnitTree: async (params: UnitTreeQueryParams = {}): Promise<Unit[]> => {
    try {
      // 调用权限管理API获取组织机构树数据
      const resp: ApiResponse<Unit[]> = await rbacRequest.get('/unit/unitTree', {
        params,
      });

      // 使用yth-ui的formatTree工具函数格式化树形数据
      // 第二个参数'unitType'用于分组，第三个参数'unitName'用于显示
      return formatTree(resp.data, 'unitType', 'unitName');
    } catch (error) {
      // 记录错误信息并重新抛出，便于上层处理
      console.error('获取组织机构树数据失败:', error);
      throw error;
    }
  },
};

export default baseApi;
