import { trainRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  aescs?: string[];
  descs?: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 考核人员安排查询参数接口
 */
export interface AssessmentPersonnelQueryParams {
  /** 计划编码 */
  planCode?: string;
  /** 计划名称 */
  planName?: string;
  /** 公司名称 */
  companyId?: string;
  /** 培训类型 */
  trainingType?: string;
  /** 培训课程名称 */
  trainingName?: string;
  /** 学习开始时间 */
  learnStartDate?: string;
  /** 学习结束时间 */
  learnEndDate?: string;
  /** 考试开始时间 */
  examStartDate?: string;
  /** 考试结束时间 */
  examEndDate?: string;
}

/**
 * 考核人员安排数据接口
 */
export interface AssessmentPersonnel {
  /** 主键 */
  id: string;
  /** 计划编码 */
  planCode: string;
  /** 公司名称 */
  companyId: string;
  /** 计划名称 */
  planName: string;
  /** 培训类型 */
  trainingType: string;
  /** 培训课程 */
  trainingId: string;
  /** 培训课程名称 */
  trainingName: string;
  /** 学习时长 */
  learningDuration: string;
  /** 学习对象 */
  learningObject: string;
  /** 学习开始时间 */
  learnStartDate: string;
  /** 学习结束时间 */
  learnEndDate: string;
  /** 培训课件 */
  trainingAttachmentPath?: string;
  /** 培训时间 */
  trainingDate: string;
  /** 培训地点 */
  trainingPlace: string;
  /** 授课教师 */
  lecturer: string;
  /** 是否考试 */
  isExam: boolean;
  /** 考试试卷 */
  examPaperId?: string;
  /** 考试试卷名称 */
  examPaperName?: string;
  /** 考试开始时间 */
  examStartDate?: string;
  /** 考试结束时间 */
  examEndDate?: string;
  /** 合格成绩 */
  passScore?: number;
  /** 补考次数 */
  examNumber?: number;
}

/**
 * 考核人员详情数据接口
 */
export interface AssessmentPersonnelDetail {
  /** 主键 */
  id: string;
  /** 考核安排ID */
  assessmentId: string;
  /** 人员ID */
  personnelId: string;
  /** 人员姓名 */
  personnelName: string;
  /** 人员工号 */
  personnelCode: string;
  /** 人员部门 */
  departmentName: string;
  /** 人员岗位 */
  postName: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 安排状态 1-已安排 2-已确认 3-已完成 */
  arrangeStatus: string;
  /** 安排状态文本 */
  arrangeStatusText?: string;
  /** 安排时间 */
  arrangeTime: string;
  /** 确认时间 */
  confirmTime?: string;
  /** 完成时间 */
  completeTime?: string;
  /** 备注 */
  remark?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 考核人员安排保存参数接口
 */
export interface AssessmentPersonnelSaveParams {
  /** 主键，新增时为空 */
  id?: string;
  /** 计划编码 */
  planCode: string;
  /** 公司名称 */
  companyId: string;
  /** 计划名称 */
  planName: string;
  /** 培训类型 */
  trainingType: string;
  /** 培训课程 */
  trainingId: string;
  /** 培训课程名称 */
  trainingName: string;
  /** 学习时长 */
  learningDuration: string;
  /** 学习对象 */
  learningObject: string;
  /** 学习开始时间 */
  learnStartDate?: string;
  /** 学习结束时间 */
  learnEndDate?: string;
  /** 培训课件 */
  trainingAttachmentPath?: string;
  /** 培训时间 */
  trainingDate: string;
  /** 培训地点 */
  trainingPlace: string;
  /** 授课教师 */
  lecturer: string;
  /** 是否考试 */
  isExam: boolean;
  /** 考试试卷 */
  examPaperId?: string;
  /** 考试试卷名称 */
  examPaperName?: string;
  /** 考试开始时间 */
  examStartDate?: string;
  /** 考试结束时间 */
  examEndDate?: string;
  /** 合格成绩 */
  passScore?: number;
  /** 补考次数 */
  examNumber?: number;
}

/**
 * 考核人员安排 API 类型定义
 */
type AssessmentApi = {
  /** 查询考核人员安排列表 */
  queryAssessmentPersonnelList: (
    data: PageQueryParams<AssessmentPersonnelQueryParams>,
  ) => Promise<ApiResponse<AssessmentPersonnel[]>>;

  /** 查询考核人员详情列表 */
  queryAssessmentPersonnelDetailList: (
    data: PageQueryParams<{ assessmentId: string }>,
  ) => Promise<ApiResponse<AssessmentPersonnelDetail[]>>;

  /** 保存考核人员安排 */
  saveAssessmentPersonnel: (data: AssessmentPersonnelSaveParams) => Promise<ApiResponse<boolean>>;

  /** 删除考核人员安排 */
  deleteAssessmentPersonnel: (id: string) => Promise<ApiResponse<boolean>>;

  /** 获取考核人员安排详情 */
  getAssessmentPersonnelDetail: (id: string) => Promise<ApiResponse<AssessmentPersonnel>>;
};

/**
 * 考核人员安排模块 API
 */
const assessmentApi: AssessmentApi = {
  /** 查询考核人员安排列表 */
  queryAssessmentPersonnelList: (data) => {
    return trainRequest.post('/assessment/personnel/page', { data });
  },

  /** 查询考核人员详情列表 */
  queryAssessmentPersonnelDetailList: (data) => {
    return trainRequest.post('/assessment/personnel/detail/page', { data });
  },

  /** 保存考核人员安排 */
  saveAssessmentPersonnel: (data) => {
    return trainRequest.post('/assessment/personnel/save', { data });
  },

  /** 删除考核人员安排 */
  deleteAssessmentPersonnel: (id) => {
    return trainRequest.delete(`/assessment/personnel/${id}`);
  },

  /** 获取考核人员安排详情 */
  getAssessmentPersonnelDetail: (id) => {
    return trainRequest.get(`/assessment/personnel/${id}`);
  },
};

export default assessmentApi;
